/**
 * 模型设置页面修复样式
 * 解决选择项透明度和滚动问题
 */

/* 修复选择框透明度问题 - 全局应用 */
.ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  opacity: 1 !important;
  z-index: 9999 !important;
}

/* 特别针对自动化运维管理页面的选择框 */
.automation-manager .ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  opacity: 1 !important;
  z-index: 9999 !important;
}

.ant-select-item-option {
  background-color: #ffffff !important;
  color: #1e293b !important;
  padding: 8px 12px !important;
  opacity: 1 !important;
  border: none !important;
}

.ant-select-item-option:hover {
  background-color: #f1f5f9 !important;
  color: #1e293b !important;
  opacity: 1 !important;
}

.ant-select-item-option-selected {
  background-color: #eff6ff !important;
  color: #1e40af !important;
  font-weight: 500 !important;
  opacity: 1 !important;
}

.ant-select-item-option-active {
  background-color: #f1f5f9 !important;
  color: #1e293b !important;
  opacity: 1 !important;
}

/* 修复模型列表滚动问题 */
.model-list {
  max-height: 300px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding-right: 8px !important;
}

/* 为设置页面添加滚动支持 */
.user-config-tab .ant-tabs-content-holder,
.user-config-tab .ant-tabs-tabpane {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  max-height: calc(100vh - 120px) !important;
}

/* 修复API配置保存按钮样式 */
.api-action-buttons {
  display: flex !important;
  gap: 8px !important;
  margin-top: 8px !important;
  z-index: 10 !important;
  position: relative !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

.api-action-buttons .save-btn {
  background-color: #1890ff !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 4px 15px !important;
  font-size: 12px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  min-width: 80px !important;
  height: 28px !important;
  line-height: 20px !important;
}

.api-action-buttons .save-btn:hover {
  background-color: #40a9ff !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3) !important;
}

.api-action-buttons .save-btn:active {
  transform: translateY(0) !important;
  background-color: #096dd9 !important;
}

.api-action-buttons .check-btn {
  background-color: #52c41a !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 4px 15px !important;
  font-size: 12px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  min-width: 80px !important;
  height: 28px !important;
  line-height: 20px !important;
}

.api-action-buttons .check-btn:hover {
  background-color: #73d13d !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3) !important;
}

.api-action-buttons .check-btn:active {
  transform: translateY(0) !important;
  background-color: #389e0d !important;
}

.api-action-buttons .check-btn:disabled {
  background-color: #d9d9d9 !important;
  color: #999999 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 确保按钮在所有主题下都可见 */
[data-theme='dark'] .api-action-buttons .save-btn {
  background-color: #1890ff !important;
  color: white !important;
}

[data-theme='dark'] .api-action-buttons .save-btn:hover {
  background-color: #40a9ff !important;
  color: white !important;
}

[data-theme='dark'] .api-action-buttons .check-btn {
  background-color: #52c41a !important;
  color: white !important;
}

[data-theme='dark'] .api-action-buttons .check-btn:hover {
  background-color: #73d13d !important;
  color: white !important;
}

/* 修复选择框在暗色主题下的透明度 */
[data-theme='dark'] .ant-select-dropdown {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
  opacity: 1 !important;
}

[data-theme='dark'] .ant-select-item-option {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
  opacity: 1 !important;
}

[data-theme='dark'] .ant-select-item-option:hover {
  background-color: #374151 !important;
  color: #f9fafb !important;
  opacity: 1 !important;
}

[data-theme='dark'] .ant-select-item-option-selected {
  background-color: #1e40af !important;
  color: #ffffff !important;
  opacity: 1 !important;
}

/* 滚动条样式优化 */
.model-list::-webkit-scrollbar,
.user-config-tab .ant-tabs-content-holder::-webkit-scrollbar,
.user-config-tab .ant-tabs-tabpane::-webkit-scrollbar {
  width: 6px !important;
}

.model-list::-webkit-scrollbar-track,
.user-config-tab .ant-tabs-content-holder::-webkit-scrollbar-track,
.user-config-tab .ant-tabs-tabpane::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 3px !important;
}

.model-list::-webkit-scrollbar-thumb,
.user-config-tab .ant-tabs-content-holder::-webkit-scrollbar-thumb,
.user-config-tab .ant-tabs-tabpane::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.3) !important;
  border-radius: 3px !important;
}

.model-list::-webkit-scrollbar-thumb:hover,
.user-config-tab .ant-tabs-content-holder::-webkit-scrollbar-thumb:hover,
.user-config-tab .ant-tabs-tabpane::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.5) !important;
}

/* 确保模型设置页面内容不被截断 */
.settings-section {
  margin-bottom: 16px !important;
}

.settings-section:last-child {
  margin-bottom: 32px !important;
}

/* 修复表单项间距 */
.setting-item {
  margin-bottom: 16px !important;
}

.setting-item:last-child {
  margin-bottom: 0 !important;
}

/* 确保API提供商配置区域有足够的空间 */
.api-provider-header {
  margin-bottom: 16px !important;
  padding-bottom: 8px !important;
  border-bottom: 1px solid var(--border-color, #e5e7eb) !important;
}

.api-provider-header h4 {
  margin: 0 !important;
  font-size: 16px !important;
  color: #1890ff !important;
  font-weight: 500 !important;
}
