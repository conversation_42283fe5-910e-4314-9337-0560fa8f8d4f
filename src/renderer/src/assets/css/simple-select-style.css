/**
 * 简洁的选择框样式
 * 基础、简洁、清晰的设计
 */

/* 选择框下拉菜单 - 简洁样式 */
.ant-select-dropdown,
body > .ant-select-dropdown,
div[class*='ant-select-dropdown'],
[class*='modal'] .ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  opacity: 1 !important;
  z-index: 99999 !important;
  padding: 4px 0 !important;
}

/* 选择项 - 简洁样式 */
.ant-select-item-option,
.ant-select-dropdown .ant-select-item-option,
body > .ant-select-dropdown .ant-select-item-option,
div[class*='ant-select-dropdown'] .ant-select-item-option,
[class*='modal'] .ant-select-dropdown .ant-select-item-option {
  background-color: #ffffff !important;
  color: #333333 !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  opacity: 1 !important;
  border: none !important;
  margin: 0 !important;
  transition: background-color 0.2s ease !important;
}

/* 悬停状态 - 简洁样式 */
.ant-select-item-option:hover,
.ant-select-dropdown .ant-select-item-option:hover,
body > .ant-select-dropdown .ant-select-item-option:hover,
div[class*='ant-select-dropdown'] .ant-select-item-option:hover,
[class*='modal'] .ant-select-dropdown .ant-select-item-option:hover {
  background-color: #f5f5f5 !important;
  color: #333333 !important;
  opacity: 1 !important;
}

/* 选中状态 - 简洁样式 */
.ant-select-item-option-selected,
.ant-select-dropdown .ant-select-item-option-selected,
body > .ant-select-dropdown .ant-select-item-option-selected,
div[class*='ant-select-dropdown'] .ant-select-item-option-selected,
[class*='modal'] .ant-select-dropdown .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500 !important;
  opacity: 1 !important;
}

/* 激活状态 - 简洁样式 */
.ant-select-item-option-active,
.ant-select-dropdown .ant-select-item-option-active,
body > .ant-select-dropdown .ant-select-item-option-active,
div[class*='ant-select-dropdown'] .ant-select-item-option-active,
[class*='modal'] .ant-select-dropdown .ant-select-item-option-active {
  background-color: #f5f5f5 !important;
  color: #333333 !important;
  opacity: 1 !important;
}

/* 选择框输入框 - 简洁样式 */
.ant-select-selector {
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  background-color: #ffffff !important;
  transition: border-color 0.2s ease !important;
}

.ant-select-selector:hover {
  border-color: #40a9ff !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 暗色主题适配 */
[data-theme='dark'] .ant-select-dropdown,
[data-theme='dark'] body > .ant-select-dropdown,
[data-theme='dark'] div[class*='ant-select-dropdown'],
[data-theme='dark'] [class*='modal'] .ant-select-dropdown {
  background-color: #1f1f1f !important;
  border-color: #434343 !important;
}

[data-theme='dark'] .ant-select-item-option,
[data-theme='dark'] .ant-select-dropdown .ant-select-item-option,
[data-theme='dark'] body > .ant-select-dropdown .ant-select-item-option,
[data-theme='dark'] div[class*='ant-select-dropdown'] .ant-select-item-option,
[data-theme='dark'] [class*='modal'] .ant-select-dropdown .ant-select-item-option {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

[data-theme='dark'] .ant-select-item-option:hover,
[data-theme='dark'] .ant-select-dropdown .ant-select-item-option:hover,
[data-theme='dark'] body > .ant-select-dropdown .ant-select-item-option:hover,
[data-theme='dark'] div[class*='ant-select-dropdown'] .ant-select-item-option:hover,
[data-theme='dark'] [class*='modal'] .ant-select-dropdown .ant-select-item-option:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme='dark'] .ant-select-item-option-selected,
[data-theme='dark'] .ant-select-dropdown .ant-select-item-option-selected,
[data-theme='dark'] body > .ant-select-dropdown .ant-select-item-option-selected,
[data-theme='dark'] div[class*='ant-select-dropdown'] .ant-select-item-option-selected,
[data-theme='dark'] [class*='modal'] .ant-select-dropdown .ant-select-item-option-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

[data-theme='dark'] .ant-select-selector {
  background-color: #1f1f1f !important;
  border-color: #434343 !important;
  color: #ffffff !important;
}

[data-theme='dark'] .ant-select-selector:hover {
  border-color: #40a9ff !important;
}

[data-theme='dark'] .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
}
