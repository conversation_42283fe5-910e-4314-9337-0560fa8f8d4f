<template>
  <div class="unified-data-manager">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button @click="goBack" class="btn-back">
          <ArrowLeft class="icon" />
          返回
        </button>
        <div class="title-section">
          <h1>统一数据服务管理</h1>
          <p class="subtitle">企业数据统一存储与管理中心</p>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshAllData" class="btn-refresh" :disabled="isRefreshing">
          <RefreshCw class="icon" :class="{ spinning: isRefreshing }" />
          {{ isRefreshing ? '刷新中...' : '刷新数据' }}
        </button>
        <button @click="showSettings = true" class="btn-settings">
          <Settings class="icon" />
          设置
        </button>
      </div>
    </div>

    <!-- 数据概览仪表板 -->
    <div class="dashboard">
      <div class="stats-grid">
        <div class="stat-card assets">
          <div class="stat-icon">
            <Package class="icon" />
          </div>
          <div class="stat-content">
            <h3>固定资产</h3>
            <div class="stat-number">{{ assetStats.total }}</div>
            <div class="stat-details">
              <span class="detail-item online">在线: {{ assetStats.online }}</span>
              <span class="detail-item maintenance">维护: {{ assetStats.maintenance }}</span>
            </div>
          </div>
          <div class="stat-actions">
            <button @click="navigateToModule('assets')" class="btn-manage">管理</button>
          </div>
        </div>

        <div class="stat-card networks">
          <div class="stat-icon">
            <Network class="icon" />
          </div>
          <div class="stat-content">
            <h3>网络设备</h3>
            <div class="stat-number">{{ networkStats.total }}</div>
            <div class="stat-details">
              <span class="detail-item online">在线: {{ networkStats.online }}</span>
              <span class="detail-item offline">离线: {{ networkStats.offline }}</span>
            </div>
          </div>
          <div class="stat-actions">
            <button @click="navigateToModule('networks')" class="btn-manage">管理</button>
          </div>
        </div>

        <div class="stat-card rooms">
          <div class="stat-icon">
            <Building class="icon" />
          </div>
          <div class="stat-content">
            <h3>机房管理</h3>
            <div class="stat-number">{{ roomStats.total }}</div>
            <div class="stat-details">
              <span class="detail-item normal">正常: {{ roomStats.normal }}</span>
              <span class="detail-item warning">告警: {{ roomStats.warning }}</span>
            </div>
          </div>
          <div class="stat-actions">
            <button @click="navigateToModule('rooms')" class="btn-manage">管理</button>
          </div>
        </div>

        <div class="stat-card hosts">
          <div class="stat-icon">
            <Server class="icon" />
          </div>
          <div class="stat-content">
            <h3>主机管理</h3>
            <div class="stat-number">{{ hostStats.total }}</div>
            <div class="stat-details">
              <span class="detail-item online">在线: {{ hostStats.online }}</span>
              <span class="detail-item offline">离线: {{ hostStats.offline }}</span>
            </div>
          </div>
          <div class="stat-actions">
            <button @click="navigateToModule('hosts')" class="btn-manage">管理</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能模块选项卡 -->
    <div class="module-tabs">
      <div class="tab-header">
        <button 
          v-for="tab in tabs" 
          :key="tab.key"
          @click="activeTab = tab.key"
          class="tab-button"
          :class="{ active: activeTab === tab.key }"
        >
          <component :is="tab.icon" class="tab-icon" />
          {{ tab.label }}
        </button>
      </div>

      <div class="tab-content">
        <!-- 数据迁移管理 -->
        <div v-if="activeTab === 'migration'" class="tab-panel">
          <DataMigrationPanel />
        </div>

        <!-- 跨模块通信 -->
        <div v-if="activeTab === 'communication'" class="tab-panel">
          <CrossModuleCommunication />
        </div>

        <!-- 数据分析 -->
        <div v-if="activeTab === 'analytics'" class="tab-panel">
          <div class="analytics-panel">
            <h3>数据分析与报表</h3>
            
            <!-- 资源分布图表 -->
            <div class="chart-section">
              <h4>资源分布统计</h4>
              <div class="chart-grid">
                <div class="chart-item">
                  <h5>资产类别分布</h5>
                  <div class="pie-chart">
                    <div 
                      v-for="(count, category) in assetsByCategory" 
                      :key="category"
                      class="pie-segment"
                      :style="{ '--percentage': (count / assetStats.total * 100) + '%' }"
                    >
                      {{ category }}: {{ count }}
                    </div>
                  </div>
                </div>
                
                <div class="chart-item">
                  <h5>设备类型分布</h5>
                  <div class="bar-chart">
                    <div 
                      v-for="(count, type) in devicesByType" 
                      :key="type"
                      class="bar-item"
                    >
                      <div class="bar-label">{{ type }}</div>
                      <div class="bar-container">
                        <div 
                          class="bar-fill" 
                          :style="{ width: (count / networkStats.total * 100) + '%' }"
                        ></div>
                        <span class="bar-value">{{ count }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 关联关系分析 -->
            <div class="relations-section">
              <h4>跨模块关联关系</h4>
              <div class="relations-grid">
                <div class="relation-card">
                  <h5>资产-网络设备关联</h5>
                  <div class="relation-count">{{ assetNetworkRelations.length }} 个关联</div>
                  <div class="relation-list">
                    <div 
                      v-for="relation in assetNetworkRelations.slice(0, 3)" 
                      :key="relation.id"
                      class="relation-item"
                    >
                      <span class="asset-name">{{ relation.assetName }}</span>
                      <span class="relation-arrow">→</span>
                      <span class="network-ip">{{ relation.networkIp }}</span>
                    </div>
                  </div>
                  <button v-if="assetNetworkRelations.length > 3" class="btn-view-more">
                    查看更多 ({{ assetNetworkRelations.length - 3 }})
                  </button>
                </div>

                <div class="relation-card">
                  <h5>机房设备分布</h5>
                  <div class="room-distribution">
                    <div 
                      v-for="room in roomDeviceDistribution.slice(0, 3)" 
                      :key="room.roomName"
                      class="room-item"
                    >
                      <div class="room-name">{{ room.roomName }}</div>
                      <div class="room-counts">
                        <span>资产: {{ room.assetCount }}</span>
                        <span>设备: {{ room.networkCount }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统测试 -->
        <div v-if="activeTab === 'testing'" class="tab-panel">
          <MigrationTestSuite />
        </div>

        <!-- 系统监控 -->
        <div v-if="activeTab === 'monitoring'" class="tab-panel">
          <div class="monitoring-panel">
            <h3>系统监控</h3>
            
            <!-- 实时状态 -->
            <div class="status-section">
              <h4>系统状态</h4>
              <div class="status-grid">
                <div class="status-item">
                  <div class="status-indicator online"></div>
                  <span>统一数据服务</span>
                  <span class="status-text">正常运行</span>
                </div>
                <div class="status-item">
                  <div class="status-indicator online"></div>
                  <span>事件总线</span>
                  <span class="status-text">正常通信</span>
                </div>
                <div class="status-item">
                  <div class="status-indicator online"></div>
                  <span>数据存储</span>
                  <span class="status-text">读写正常</span>
                </div>
              </div>
            </div>

            <!-- 性能指标 -->
            <div class="performance-section">
              <h4>性能指标</h4>
              <div class="metrics-grid">
                <div class="metric-card">
                  <h5>数据操作响应时间</h5>
                  <div class="metric-value">{{ performanceMetrics.avgResponseTime }}ms</div>
                  <div class="metric-trend">较昨日 -5%</div>
                </div>
                <div class="metric-card">
                  <h5>存储使用率</h5>
                  <div class="metric-value">{{ performanceMetrics.storageUsage }}%</div>
                  <div class="metric-trend">较昨日 +2%</div>
                </div>
                <div class="metric-card">
                  <h5>事件处理量</h5>
                  <div class="metric-value">{{ performanceMetrics.eventCount }}</div>
                  <div class="metric-trend">较昨日 +12%</div>
                </div>
              </div>
            </div>

            <!-- 最近活动 -->
            <div class="activity-section">
              <h4>最近活动</h4>
              <div class="activity-list">
                <div 
                  v-for="activity in recentActivities" 
                  :key="activity.id"
                  class="activity-item"
                >
                  <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
                  <div class="activity-content">
                    <span class="activity-type">{{ activity.type }}</span>
                    <span class="activity-description">{{ activity.description }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <div v-if="showSettings" class="modal-overlay" @click="showSettings = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>系统设置</h3>
          <button @click="showSettings = false" class="btn-close">×</button>
        </div>
        <div class="modal-body">
          <div class="setting-group">
            <h4>数据存储设置</h4>
            <div class="setting-item">
              <label>
                <input type="checkbox" v-model="settings.autoBackup" />
                自动数据备份
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" v-model="settings.realTimeSync" />
                实时数据同步
              </label>
            </div>
          </div>
          
          <div class="setting-group">
            <h4>通信设置</h4>
            <div class="setting-item">
              <label>
                <input type="checkbox" v-model="settings.eventLogging" />
                事件日志记录
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" v-model="settings.crossModuleNotify" />
                跨模块通知
              </label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="saveSettings" class="btn-save">保存设置</button>
          <button @click="showSettings = false" class="btn-cancel">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, RefreshCw, Settings, Package, Network, Building, Server,
  Database, MessageSquare, BarChart3, TestTube, Activity
} from 'lucide-vue-next'
import unifiedDataService from '../../services/unifiedEnterpriseDataService'
import DataMigrationPanel from '../../components/DataMigrationPanel.vue'
import CrossModuleCommunication from '../../components/CrossModuleCommunication.vue'
import MigrationTestSuite from '../../components/MigrationTestSuite.vue'

const router = useRouter()

// 响应式数据
const isRefreshing = ref(false)
const showSettings = ref(false)
const activeTab = ref('migration')

// 设置
const settings = ref({
  autoBackup: true,
  realTimeSync: true,
  eventLogging: true,
  crossModuleNotify: true
})

// 选项卡配置
const tabs = [
  { key: 'migration', label: '数据迁移', icon: Database },
  { key: 'communication', label: '跨模块通信', icon: MessageSquare },
  { key: 'analytics', label: '数据分析', icon: BarChart3 },
  { key: 'testing', label: '系统测试', icon: TestTube },
  { key: 'monitoring', label: '系统监控', icon: Activity }
]

// 模拟数据
const performanceMetrics = ref({
  avgResponseTime: 45,
  storageUsage: 68,
  eventCount: 1247
})

const recentActivities = ref([
  {
    id: 1,
    timestamp: new Date().toISOString(),
    type: '数据迁移',
    description: '完成资产管理数据迁移，共迁移 156 条记录'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 300000).toISOString(),
    type: '设备添加',
    description: '新增网络设备 Core-Switch-02'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 600000).toISOString(),
    type: '状态更新',
    description: '资产 IT-001 状态更新为维护中'
  }
])

// 计算属性
const assetStats = computed(() => {
  const assets = unifiedDataService.getAssets()
  return {
    total: assets.length,
    online: assets.filter(a => a.status === 'online' || a.status === 'normal').length,
    maintenance: assets.filter(a => a.status === 'maintenance').length
  }
})

const networkStats = computed(() => {
  const devices = unifiedDataService.getNetworkDevices()
  return {
    total: devices.length,
    online: devices.filter(d => d.status === 'online').length,
    offline: devices.filter(d => d.status === 'offline').length
  }
})

const roomStats = computed(() => {
  const rooms = unifiedDataService.getRooms()
  return {
    total: rooms.length,
    normal: rooms.filter(r => r.status === 'normal').length,
    warning: rooms.filter(r => r.status === 'warning').length
  }
})

const hostStats = computed(() => {
  const hosts = unifiedDataService.getHosts()
  return {
    total: hosts.length,
    online: hosts.filter(h => h.status === 'online').length,
    offline: hosts.filter(h => h.status === 'offline').length
  }
})

const assetsByCategory = computed(() => {
  const assets = unifiedDataService.getAssets()
  const categories: Record<string, number> = {}
  assets.forEach(asset => {
    categories[asset.category] = (categories[asset.category] || 0) + 1
  })
  return categories
})

const devicesByType = computed(() => {
  const devices = unifiedDataService.getNetworkDevices()
  const types: Record<string, number> = {}
  devices.forEach(device => {
    types[device.deviceType] = (types[device.deviceType] || 0) + 1
  })
  return types
})

const assetNetworkRelations = computed(() => {
  const assets = unifiedDataService.getAssets()
  const networks = unifiedDataService.getNetworkDevices()
  
  return assets
    .filter(asset => asset.ipAddress)
    .map(asset => {
      const matchedNetwork = networks.find(net => net.ip === asset.ipAddress)
      if (matchedNetwork) {
        return {
          id: `${asset.id}-${matchedNetwork.id}`,
          assetName: asset.name,
          networkIp: matchedNetwork.ip
        }
      }
      return null
    })
    .filter(Boolean)
})

const roomDeviceDistribution = computed(() => {
  const rooms = unifiedDataService.getRooms()
  const assets = unifiedDataService.getAssets()
  const networks = unifiedDataService.getNetworkDevices()
  
  return rooms.map(room => ({
    roomName: room.name,
    assetCount: assets.filter(a => a.location?.includes(room.name)).length,
    networkCount: networks.filter(n => n.location?.includes(room.name)).length
  }))
})

// 方法
const goBack = () => {
  router.push('/')
}

const refreshAllData = async () => {
  isRefreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里可以添加实际的数据刷新逻辑
  } finally {
    isRefreshing.value = false
  }
}

const navigateToModule = (module: string) => {
  const routes: Record<string, string> = {
    'assets': '/enterprise/asset-management',
    'networks': '/enterprise/network/devices-monitor',
    'rooms': '/enterprise/room-management',
    'hosts': '/enterprise/host-management'
  }
  
  if (routes[module]) {
    router.push(routes[module])
  }
}

const saveSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('unified_data_service_settings', JSON.stringify(settings.value))
  showSettings.value = false
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 加载设置
  const savedSettings = localStorage.getItem('unified_data_service_settings')
  if (savedSettings) {
    settings.value = { ...settings.value, ...JSON.parse(savedSettings) }
  }
})
</script>

<style scoped>
.unified-data-manager {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-back:hover {
  background: #e9ecef;
}

.title-section h1 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 24px;
}

.subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-refresh, .btn-settings {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-refresh {
  background: #007bff;
  color: white;
}

.btn-refresh:hover:not(:disabled) {
  background: #0056b3;
}

.btn-refresh:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-settings {
  background: #6c757d;
  color: white;
}

.btn-settings:hover {
  background: #545b62;
}

.icon {
  width: 16px;
  height: 16px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.dashboard {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card.assets .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.networks .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.rooms .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.hosts .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stat-details {
  display: flex;
  gap: 12px;
}

.detail-item {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
}

.detail-item.online {
  background: #d4edda;
  color: #155724;
}

.detail-item.maintenance {
  background: #fff3cd;
  color: #856404;
}

.detail-item.offline {
  background: #f8d7da;
  color: #721c24;
}

.detail-item.normal {
  background: #d4edda;
  color: #155724;
}

.detail-item.warning {
  background: #fff3cd;
  color: #856404;
}

.btn-manage {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.btn-manage:hover {
  background: #0056b3;
}

.module-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  color: #6c757d;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: #f8f9fa;
}

.tab-icon {
  width: 16px;
  height: 16px;
}

.tab-content {
  padding: 24px;
}

.analytics-panel, .monitoring-panel {
  max-width: none;
}

.chart-section, .relations-section, .status-section, .performance-section, .activity-section {
  margin-bottom: 32px;
}

.chart-section h4, .relations-section h4, .status-section h4, .performance-section h4, .activity-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.chart-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.chart-item h5 {
  margin: 0 0 16px 0;
  color: #495057;
}

.pie-chart, .bar-chart {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pie-segment, .bar-item {
  padding: 8px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  width: 80px;
  font-size: 12px;
  color: #6c757d;
}

.bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar-fill {
  height: 8px;
  background: #007bff;
  border-radius: 4px;
  min-width: 4px;
}

.bar-value {
  font-size: 12px;
  color: #6c757d;
}

.relations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.relation-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.relation-card h5 {
  margin: 0 0 12px 0;
  color: #495057;
}

.relation-count {
  font-size: 18px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 16px;
}

.relation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.relation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.asset-name {
  color: #2c3e50;
  font-weight: 500;
}

.relation-arrow {
  color: #6c757d;
}

.network-ip {
  color: #007bff;
  font-family: monospace;
}

.btn-view-more {
  padding: 4px 8px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.room-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.room-name {
  font-weight: 500;
  color: #2c3e50;
}

.room-counts {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #28a745;
}

.status-text {
  margin-left: auto;
  font-size: 12px;
  color: #28a745;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.metric-card h5 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  color: #28a745;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-time {
  width: 80px;
  font-size: 12px;
  color: #6c757d;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-type {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 8px;
}

.activity-description {
  color: #495057;
  font-size: 14px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  color: #495057;
}

.setting-item {
  margin-bottom: 12px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #dee2e6;
}

.btn-save {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-cancel {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
