<template>
  <div class="user-config">
    <div class="user-config-title">{{ $t('common.userConfig') }}</div>
    <a-divider style="border-color: var(--border-color); margin: 0 0 0 0" />
    <div class="tabs-container">
      <a-tabs
        default-active-key="0"
        tab-position="left"
        class="user-config-tab"
      >
        <a-tab-pane
          key="0"
          :tab="$t('user.general')"
          force-render
          type="card"
        >
          <General />
        </a-tab-pane>
        <a-tab-pane
          key="1"
          :tab="$t('user.terminal')"
          force-render
          type="card"
        >
          <TerminalSimple />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          :tab="$t('user.extensions')"
          type="card"
        >
          <Extensions />
        </a-tab-pane>
        <a-tab-pane
          key="3"
          :tab="$t('user.models')"
          type="card"
        >
          <Model />
        </a-tab-pane>
        <a-tab-pane
          key="4"
          :tab="$t('user.aiPreferences')"
          type="card"
        >
          <AI />
        </a-tab-pane>
        <a-tab-pane
          key="5"
          :tab="$t('user.shortcuts')"
          type="card"
        >
          <Shortcuts />
        </a-tab-pane>
        <a-tab-pane
          key="6"
          :tab="$t('user.privacy')"
          type="card"
        >
          <Privacy />
        </a-tab-pane>
        <a-tab-pane
          key="7"
          :tab="$t('user.rules')"
          type="card"
        >
          <Rules />
        </a-tab-pane>
        <a-tab-pane
          key="8"
          :tab="$t('user.about')"
          type="card"
        >
          <About />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, defineAsyncComponent } from 'vue'

// 使用动态导入来避免 TypeScript 错误
const General = defineAsyncComponent(() => import('./components/general.vue'))
const TerminalSimple = defineAsyncComponent(() => import('./components/terminal-simple.vue'))
const Extensions = defineAsyncComponent(() => import('./components/extensions.vue'))
const AI = defineAsyncComponent(() => import('./components/ai.vue'))
const Model = defineAsyncComponent(() => import('./components/model.vue'))
const Shortcuts = defineAsyncComponent(() => import('./components/shortcuts.vue'))
const Privacy = defineAsyncComponent(() => import('./components/privacy.vue'))
const Rules = defineAsyncComponent(() => import('./components/rules.vue'))
const About = defineAsyncComponent(() => import('./components/about.vue'))

onMounted(() => {
  console.log('UserConfig component mounted')
})
</script>

<style lang="less" scoped>
.user-config {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.user-config-title {
  line-height: 30px;
  font-size: 16px;
  font-weight: 600;
  margin-left: 10px;
  flex-shrink: 0;
  color: var(--text-color);
}

.tabs-container {
  flex: 1;
  overflow: hidden;
}

.user-config-tab {
  color: var(--text-color);
  height: 100%;

  :deep(.ant-tabs) {
    height: 100%;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }

  :deep(.ant-tabs-nav) {
    height: 100%;
    width: 120px;
    background-color: var(--bg-color);

    &::before {
      display: none;
    }
  }

  :deep(.ant-tabs-content-holder) {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: var(--bg-color);
    padding-right: 8px;

    /* 显示滚动条以便用户知道可以滚动 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(24, 144, 255, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(24, 144, 255, 0.5);
    }

    scrollbar-width: thin;
    scrollbar-color: rgba(24, 144, 255, 0.3) transparent;
  }

  :deep(.ant-tabs-tabpane) {
    padding-left: 0 !important;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: var(--bg-color);
    padding-right: 8px;

    /* 显示滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(24, 144, 255, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(24, 144, 255, 0.5);
    }

    scrollbar-width: thin;
    scrollbar-color: rgba(24, 144, 255, 0.3) transparent;
  }

  :deep(.ant-tabs-nav-list) {
    border-right: 1px solid var(--bg-color);
    height: 100%;
  }

  :deep(.ant-tabs-tab) {
    padding: 8px 16px !important;
    margin: 0 !important;
    min-height: 40px;
    font-size: 14px;
    color: var(--text-color-secondary);
  }

  :deep(.ant-tabs-tab-active) {
    background-color: var(--hover-bg-color);
    .ant-tabs-tab-btn {
      color: #1890ff !important;
    }
  }

  :deep(.ant-tabs-content-holder) {
    height: 100%;
    overflow: auto;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
  }
}
</style>
