# AI功能选择框重构

## 问题描述
用户反馈AI功能的模式和模型选择框显示不清楚，选中的内容看不清楚。

## 重构方案

### 1. 结构重构
将原来的简单选择框重构为带标签的选择器组件：

```vue
<!-- 重构前 -->
<a-select v-model:value="chatTypeValue" size="small" style="width: 100px" />
<a-select v-model:value="chatAiModelValue" size="small" style="width: 150px" />

<!-- 重构后 -->
<div class="ai-mode-selector">
  <label class="selector-label">模式</label>
  <a-select v-model:value="chatTypeValue" class="ai-mode-select" />
</div>
<div class="ai-model-selector">
  <label class="selector-label">模型</label>
  <a-select v-model:value="chatAiModelValue" class="ai-model-select" />
</div>
```

### 2. 视觉改进

#### 2.1 标签设计
- 添加了"模式"和"模型"标签
- 使用小号字体 (11px) 和大写字母
- 灰色文字，增加字母间距

#### 2.2 选择框设计
- **边框**: 2px solid，圆角8px
- **颜色**: 默认 #e1e5e9，悬停/聚焦 #1890ff
- **阴影**: 基础阴影 + 悬停/聚焦增强阴影
- **高度**: 最小36px，确保足够的点击区域

#### 2.3 内容显示
- **字体**: 600字重，13px大小
- **颜色**: 深色 #1a1a1a，确保高对比度
- **占位符**: 浅灰色，明确提示用户操作

#### 2.4 交互效果
- **悬停**: 边框变蓝，阴影增强
- **聚焦**: 蓝色边框 + 外发光效果
- **下拉箭头**: 旋转动画，视觉反馈

### 3. 暗色主题适配
完整支持暗色主题：
- 背景色: #1f1f1f
- 边框色: #434343
- 文字色: #ffffff
- 标签色: #cccccc
- 阴影: 调整透明度适配暗色背景

### 4. 技术实现

#### 4.1 HTML结构
```vue
<div class="input-controls">
  <!-- AI模式选择 -->
  <div class="ai-mode-selector">
    <label class="selector-label">模式</label>
    <a-select
      v-model:value="chatTypeValue"
      size="small"
      class="ai-mode-select"
      :options="AiTypeOptions"
      placeholder="选择模式"
      :allow-clear="false"
      :get-popup-container="(trigger) => trigger.parentNode"
    >
      <template #suffixIcon>
        <CaretDownOutlined class="select-arrow" />
      </template>
    </a-select>
  </div>
  
  <!-- AI模型选择 -->
  <div class="ai-model-selector">
    <label class="selector-label">模型</label>
    <a-select
      v-model:value="chatAiModelValue"
      size="small"
      class="ai-model-select"
      placeholder="选择模型"
      :allow-clear="false"
      :get-popup-container="(trigger) => trigger.parentNode"
      @change="handleChatAiModelChange"
    >
      <template #suffixIcon>
        <CaretDownOutlined class="select-arrow" />
      </template>
      <a-select-option
        v-for="model in AgentAiModelsOptions"
        :key="model.value"
        :value="model.value"
      >
        <span class="model-option-label">
          <img
            v-if="model.label.endsWith('-Thinking')"
            src="@/assets/icons/thinking.svg"
            alt="Thinking"
            class="thinking-icon"
          />
          {{ model.label.replace(/-Thinking$/, '') }}
        </span>
      </a-select-option>
    </a-select>
  </div>
</div>
```

#### 4.2 关键CSS类
- `.ai-mode-selector` / `.ai-model-selector`: 容器布局
- `.selector-label`: 标签样式
- `.ai-mode-select` / `.ai-model-select`: 选择框样式
- `.select-arrow`: 自定义下拉箭头
- `.model-option-label`: 模型选项标签

#### 4.3 响应式设计
- 模式选择器: 最小宽度100px
- 模型选择器: 最小宽度160px，flex: 1自适应
- 容器间距: 12px gap

### 5. 用户体验改进

#### 5.1 可见性提升
- 高对比度文字颜色
- 清晰的边框和阴影
- 明确的标签指示

#### 5.2 交互反馈
- 悬停状态视觉变化
- 聚焦状态外发光效果
- 下拉箭头旋转动画

#### 5.3 可用性增强
- 禁用清除按钮，避免意外清空
- 自定义下拉容器，避免层级问题
- 占位符文字提示用户操作

### 6. 兼容性保证
- 保持原有的数据绑定逻辑
- 保持原有的事件处理函数
- 保持原有的选项数据结构
- 支持亮色和暗色主题切换

## 预期效果
重构后的AI选择框将具有：
1. **更清晰的视觉层次** - 标签 + 选择框的结构
2. **更高的可读性** - 高对比度文字和边框
3. **更好的交互体验** - 丰富的视觉反馈
4. **更强的品牌一致性** - 统一的设计语言
5. **更完善的主题支持** - 亮色/暗色主题适配

用户现在可以清楚地看到当前选择的AI模式和模型，并且能够轻松地进行切换操作。
